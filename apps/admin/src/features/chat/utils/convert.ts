import { TreeDataItem } from '@/components/ui/tree';
import { LucideIcon } from 'lucide-react';

export type FileSystemTree = {
  [key: string]: { file: { contents: string } } | { directory: FileSystemTree };
};

export type FileDeletions = {
  filesToDelete: string[];
};

export function extractFileDeletions(message: string): FileDeletions {
  // Match DELETE_FILE actions with filePath
  const deleteActionRegex =
    /<fullstackfoxAction[^>]*type="DELETE_FILE"[^>]*filePath="(.+?)"[^>]*(?:\/>|><\/fullstackfoxAction>)/g;

  const filesToDelete: string[] = [];
  let match;

  while ((match = deleteActionRegex.exec(message)) !== null) {
    const filePath = match[1].trim();
    if (filePath && !filesToDelete.includes(filePath)) {
      filesToDelete.push(filePath);
    }
  }

  return { filesToDelete };
}

/**
 * Extracts file paths from a message in the order they appear
 * @param message The message content to extract file paths from
 * @returns Array of file paths in the order they appear in the message
 */
export function extractFilePathsInOrder(message: string): string[] {
  // Match both complete and incomplete fullstackfoxAction blocks with type="file"
  const fileActionRegex =
    /<fullstackfoxAction[^>]*type="file"[^>]*filePath="(.+?)"[^>]*>/g;

  const filePaths: string[] = [];
  let match;

  while ((match = fileActionRegex.exec(message)) !== null) {
    const filePath = match[1].trim();
    if (filePath && !filePaths.includes(filePath)) {
      filePaths.push(filePath);
    }
  }

  return filePaths;
}

export function convertTextToWebContainerTree(message: string): FileSystemTree {
  // Match complete fullstackfoxAction blocks with type="file" and filePath and inner contents
  const completeActionRegex =
    /<fullstackfoxAction[^>]*type="file"[^>]*filePath="(.+?)"[^>]*>([\s\S]*?)<\/fullstackfoxAction>/g;

  // First process all complete matches
  const completeMatches = [...message.matchAll(completeActionRegex)];

  // Find all opening tags with type="file"
  const openingTagRegex =
    /<fullstackfoxAction[^>]*type="file"[^>]*filePath="(.+?)"[^>]*>/g;
  const allOpeningTags = [...message.matchAll(openingTagRegex)];

  // Find all closing tags
  const closingTagRegex = /<\/fullstackfoxAction>/g;
  const allClosingTags = [...message.matchAll(closingTagRegex)];

  // Combine complete matches with any incomplete matches
  const allMatches = [...completeMatches];

  // If we have more opening tags than closing tags, we have incomplete tags
  if (allOpeningTags.length > allClosingTags.length) {
    // For each opening tag, check if it has a corresponding closing tag
    for (let i = 0; i < allOpeningTags.length; i++) {
      const openingTag = allOpeningTags[i];
      const openingTagIndex = openingTag.index!;

      // Check if this opening tag is already part of a complete match
      const isPartOfCompleteMatch = completeMatches.some((match) => {
        // The match's index would be the same as the opening tag's index
        return match.index === openingTagIndex;
      });

      if (!isPartOfCompleteMatch) {
        // This is an incomplete tag, extract the file path
        const filePath = openingTag[1].trim();

        // Find the content between this opening tag and the end of the message or the next opening tag
        const tagContent = openingTag[0];
        const contentStartIndex = openingTagIndex + tagContent.length;

        // Find the next opening tag or the end of the message
        const nextOpeningTagIndex =
          i < allOpeningTags.length - 1
            ? allOpeningTags[i + 1].index
            : message.length;

        // Extract the content
        const fileContent = message
          .substring(contentStartIndex, nextOpeningTagIndex)
          .trim()
          // Remove any closing tags that might be in the content
          .replace(/<\/fullstackfoxAction>/g, '');

        // Create a synthetic match object
        const syntheticMatch = [
          '', // Full match (not needed)
          filePath,
          fileContent,
        ] as unknown as RegExpExecArray;
        syntheticMatch.index = openingTagIndex;

        allMatches.push(syntheticMatch);
      }
    }
  }

  // Process each match and reduce into a file tree
  return allMatches.reduce((fileTree, match) => {
    const filePath = match[1].trim();
    const fileContent = match[2].trim();

    // Split the path and get the filename
    const pathParts = filePath.split('/');
    const fileName = pathParts.pop()!;

    // Create the nested path in the tree
    const targetDirectory = pathParts.reduce((currentLevel, pathPart) => {
      // Create directory if it doesn't exist
      if (!currentLevel[pathPart]) {
        currentLevel[pathPart] = { directory: {} };
      }

      return (currentLevel[pathPart] as { directory: FileSystemTree })
        .directory;
    }, fileTree);

    // Add the file to the target directory
    targetDirectory[fileName] = {
      file: {
        contents: fileContent,
      },
    };

    return fileTree;
  }, {} as FileSystemTree);
}

export function convertFileTreeToTreeData(
  fileTree: FileSystemTree,
  folderIcon?: LucideIcon,
  fileIcon?: LucideIcon,
): TreeDataItem[] | TreeDataItem {
  let idCounter = 1;

  const generateUniqueId = (): string => {
    return `node_${idCounter++}`;
  };

  const processLevel = (
    tree: FileSystemTree,
    parentPath: string = '',
  ): TreeDataItem[] => {
    return Object.entries(tree).map(([key, value]) => {
      const currentPath = parentPath ? `${parentPath}/${key}` : key;

      if ('directory' in value) {
        // This is a directory
        const children = processLevel(value.directory, currentPath);
        return {
          id: generateUniqueId(),
          name: key,
          icon: folderIcon,
          children: children.length > 0 ? children : undefined,
          path: currentPath,
        };
      } else {
        // This is a file
        return {
          id: generateUniqueId(),
          name: key,
          icon: fileIcon,
          path: currentPath,
        };
      }
    });
  };

  const result = processLevel(fileTree);

  // If there's only one root item, return it as a single TreeDataItem
  // Otherwise return the array of TreeDataItems
  return result.length === 1 ? result[0] : result;
}

/**
 * Retrieves file contents from a FileSystemTree by path
 * @param fileTree The file system tree structure
 * @param path The path to the file (e.g. 'src/index.css')
 * @returns The file contents as a string, or null if the path doesn't exist or is a directory
 */
/**
 * Merges two file system trees, preserving existing files and adding new ones
 * @param existingTree The existing file system tree
 * @param newTree The new file system tree to merge in
 * @returns The merged file system tree
 */
export function mergeFileTrees(
  existingTree: FileSystemTree,
  newTree: FileSystemTree,
): FileSystemTree {
  const mergedTree = { ...existingTree };

  // Recursively merge the trees
  Object.entries(newTree).forEach(([key, value]) => {
    // If the key doesn't exist in the existing tree, just add it
    if (!(key in mergedTree)) {
      mergedTree[key] = value;
      return;
    }

    // If both are directories, merge their contents
    if ('directory' in value && 'directory' in mergedTree[key]) {
      const existingDir = (mergedTree[key] as { directory: FileSystemTree })
        .directory;
      const newDir = (value as { directory: FileSystemTree }).directory;

      (mergedTree[key] as { directory: FileSystemTree }).directory =
        mergeFileTrees(existingDir, newDir);
    }
    // If both are files or if the new one is a file, replace with the new one
    else {
      mergedTree[key] = value;
    }
  });

  return mergedTree;
}

/**
 * Removes files from a FileSystemTree based on file paths
 * @param fileTree The existing file system tree
 * @param filesToDelete Array of file paths to delete
 * @returns The file system tree with specified files removed
 */
export function removeFilesFromTree(
  fileTree: FileSystemTree,
  filesToDelete: string[],
): FileSystemTree {
  const updatedTree = { ...fileTree };

  filesToDelete.forEach((filePath) => {
    const pathParts = filePath.split('/');
    const fileName = pathParts.pop();

    if (!fileName) return;

    // Navigate to the parent directory
    let currentLevel = updatedTree;
    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];
      if (currentLevel[part] && 'directory' in currentLevel[part]) {
        currentLevel = (currentLevel[part] as { directory: FileSystemTree })
          .directory;
      } else {
        // Path doesn't exist, nothing to delete
        return;
      }
    }

    // Delete the file if it exists
    if (currentLevel[fileName]) {
      delete currentLevel[fileName];
    }
  });

  return updatedTree;
}

export function getFileContentsByPath(
  fileTree: FileSystemTree,
  path: string,
): string | null {
  // Split the path into parts
  const pathParts = path.split('/');

  // Start at the root of the tree
  let current:
    | FileSystemTree
    | { file: { contents: string } }
    | { directory: FileSystemTree } = fileTree;

  // Navigate through the path parts
  for (let i = 0; i < pathParts.length; i++) {
    const part = pathParts[i];

    // Check if the current part exists in the current level
    if (!(part in current)) {
      return null; // Path part doesn't exist
    }

    // If we're at the last part and current[part] has a file property
    if (
      i === pathParts.length - 1 &&
      'file' in (current as FileSystemTree)[part]
    ) {
      // Return the file contents
      return (
        (current as FileSystemTree)[part] as { file: { contents: string } }
      ).file.contents;
    }

    // If this part is a directory, move to that directory
    if ('directory' in (current as FileSystemTree)[part]) {
      current = (
        (current as FileSystemTree)[part] as { directory: FileSystemTree }
      ).directory;
    } else if (i < pathParts.length - 1) {
      // If we're not at the last part but this isn't a directory, the path is invalid
      return null;
    }
  }

  // If we get here, the path doesn't point to a file
  return null;
}
/**
 * Filters out empty files from a FileSystemTree
 * @param fileTree The file system tree to filter
 * @returns A new file system tree with empty files removed
 */
export function filterEmptyFiles(fileTree: FileSystemTree): FileSystemTree {
  const filteredTree: FileSystemTree = {};

  Object.entries(fileTree).forEach(([key, value]) => {
    if ('file' in value) {
      // Only keep files with non-empty content
      if (value.file.contents.trim() !== '') {
        filteredTree[key] = value;
      }
    } else if ('directory' in value) {
      // Recursively filter directories
      const filteredDirectory = filterEmptyFiles(value.directory);
      // Only keep directories that have content after filtering
      if (Object.keys(filteredDirectory).length > 0) {
        filteredTree[key] = { directory: filteredDirectory };
      }
    }
  });

  return filteredTree;
}

export function convertGitFilesToFileSystemTree(
  gitFiles: Record<string, string>,
): FileSystemTree {
  const fileTree: FileSystemTree = {};

  for (const [filePath, fileContent] of Object.entries(gitFiles)) {
    const pathParts = filePath.split('/');
    const fileName = pathParts.pop();

    if (!fileName) {
      console.warn(`Skipping file with invalid path: ${filePath}`);
      continue;
    }

    let currentLevel = fileTree;
    for (const part of pathParts) {
      if (!currentLevel[part]) {
        currentLevel[part] = { directory: {} };
      } else if (!('directory' in currentLevel[part])) {
        // This case should ideally not happen if paths are consistent
        // (e.g., not having a file and a directory with the same name in the path)
        console.warn(
          `Path conflict: ${part} is a file but needs to be a directory for ${filePath}`,
        );
        // Overwrite with directory to proceed, or handle error differently
        currentLevel[part] = { directory: {} };
      }
      currentLevel = (currentLevel[part] as { directory: FileSystemTree })
        .directory;
    }

    currentLevel[fileName] = {
      file: {
        contents: fileContent,
      },
    };
  }

  return fileTree;
}

/**
 * Converts a FileSystemTree back to gitFiles format (Record<string, string>)
 * @param fileTree The file system tree structure
 * @returns Record where keys are file paths and values are file contents
 */
export function convertFileSystemTreeToGitFiles(
  fileTree: FileSystemTree,
): Record<string, string> {
  const gitFiles: Record<string, string> = {};

  function traverseTree(tree: FileSystemTree, currentPath: string = ''): void {
    for (const [key, value] of Object.entries(tree)) {
      const fullPath = currentPath ? `${currentPath}/${key}` : key;

      if ('file' in value) {
        // This is a file, add it to gitFiles
        gitFiles[fullPath] = value.file.contents;
      } else if ('directory' in value) {
        // This is a directory, recursively traverse it
        traverseTree(value.directory, fullPath);
      }
    }
  }

  traverseTree(fileTree);
  return gitFiles;
}

/**
 * Checks if a file path should be ignored based on common ignore patterns
 * @param filePath The file path to check
 * @returns true if the file should be ignored, false otherwise
 */
export function shouldIgnoreFile(filePath: string): boolean {
  // Normalize path separators
  const normalizedPath = filePath.replace(/\\/g, '/');

  // Patterns to ignore
  const ignorePatterns = [
    // UI components directory
    /^src\/components\/ui\//,

    // Lock files
    /^pnpm-lock\.yaml$/,
    /^yarn\.lock$/,
    /^package-lock\.json$/,

    // Node modules
    /node_modules/,

    // Build outputs
    /\/dist\//,
    /\/build\//,
    /\/.next\//,
    /\/out\//,

    // Cache directories
    /\/.cache\//,
    /\.turbo/,

    // Environment files
    /\.env$/,
    /\.env\./,

    // IDE and editor files
    /\/.vscode\//,
    /\/.idea\//,
    /\.swp$/,
    /\.swo$/,
    /~$/,

    // OS generated files
    /\.DS_Store$/,
    /Thumbs\.db$/,
    /desktop\.ini$/,

    // Version control
    /\/.git\//,
    /\.gitignore$/,

    // Logs
    /\.log$/,
    /logs\//,
    /npm-debug\.log/,
    /yarn-debug\.log/,
    /yarn-error\.log/,
    /\.pnpm-debug\.log/,

    // Test coverage
    /\/coverage\//,
    /\.nyc_output\//,

    // Temporary files
    /\.tmp$/,
    /\.temp$/,
    /\/tmp\//,

    // Binary files and images (common ones)
    /\.(jpg|jpeg|png|gif|ico|svg|pdf|zip|tar|gz|exe|dmg)$/i,

    // TypeScript build info
    /\.tsbuildinfo$/,

    // Vercel
    /\/.vercel\//,
  ];

  return ignorePatterns.some((pattern) => pattern.test(normalizedPath));
}

/**
 * Filters out ignored files from gitFiles object
 * @param gitFiles Record of file paths to file contents
 * @returns Filtered gitFiles object with ignored files removed
 */
export function filterIgnoredFiles(
  gitFiles: Record<string, string>,
): Record<string, string> {
  const filteredFiles: Record<string, string> = {};

  for (const [filePath, content] of Object.entries(gitFiles)) {
    // Always include package.json files for dependency information
    const isPackageJson = filePath.endsWith('package.json');

    if (isPackageJson || !shouldIgnoreFile(filePath)) {
      filteredFiles[filePath] = content;
    }
  }

  return filteredFiles;
}

/**
 * Converts git files to FileSystemTree with filtering for ignored files
 * @param gitFiles Record of file paths to file contents
 * @param applyFilter Whether to filter out ignored files (default: true)
 * @returns FileSystemTree with optionally filtered files
 */
export function convertGitFilesToFileSystemTreeFiltered(
  gitFiles: Record<string, string>,
  applyFilter: boolean = true,
): FileSystemTree {
  const filesToProcess = applyFilter ? filterIgnoredFiles(gitFiles) : gitFiles;
  return convertGitFilesToFileSystemTree(filesToProcess);
}

/**
 * Removes ignored files from an existing FileSystemTree
 * @param fileTree The file system tree structure
 * @returns FileSystemTree with ignored files removed
 */
export function convertFileSystemTreeToGitFilesFiltered(
  fileTree: FileSystemTree,
): Record<string, string> {
  const gitFiles = convertFileSystemTreeToGitFiles(fileTree);
  const filteredFiles = filterIgnoredFiles(gitFiles);
  return filteredFiles;
}

/**
 * Modifies package.json files in gitFiles to add STATIC_PREVIEW=true to build scripts
 * @param gitFiles Record of file paths to file contents
 * @returns Modified gitFiles with updated package.json build scripts
 */
export function modifyPackageJsonForPreview(
  gitFiles: Record<string, string>,
): Record<string, string> {
  const modifiedFiles = { ...gitFiles };

  // Find all package.json files
  Object.keys(modifiedFiles).forEach((filePath) => {
    if (filePath.endsWith('package.json')) {
      try {
        const packageContent = JSON.parse(modifiedFiles[filePath]);

        // Check if scripts.build exists and contains "next build"
        if (packageContent.scripts?.build?.includes('next build')) {
          packageContent.scripts.build = packageContent.scripts.build.replace(
            'next build',
            'STATIC_PREVIEW=true next build',
          );

          // Update the file content with proper formatting
          modifiedFiles[filePath] = JSON.stringify(packageContent, null, 2);
        }
      } catch (error) {
        console.warn(`Failed to parse package.json at ${filePath}:`, error);
      }
    }
  });

  return modifiedFiles;
}

/**
 * Modifies files for preview mode: updates package.json build scripts, copies page route to root, and removes middleware.ts
 * @param gitFiles Record of file paths to file contents
 * @param pageRoute Optional page route (e.g., "/auth/login") to copy to root page
 * @returns Modified gitFiles with updated package.json, copied page content, and removed middleware files
 */
export function modifyFilesForPreview(
  gitFiles: Record<string, string>,
  pageRoute?: string,
): Record<string, string> {
  const modifiedFiles = { ...gitFiles };

  // 1. Modify package.json files for preview mode
  Object.keys(modifiedFiles).forEach((filePath) => {
    if (filePath.endsWith('package.json')) {
      try {
        const packageContent = JSON.parse(modifiedFiles[filePath]);

        // Check if scripts.build exists and contains "next build"
        if (packageContent.scripts?.build?.includes('next build')) {
          packageContent.scripts.build = packageContent.scripts.build.replace(
            'next build',
            'STATIC_PREVIEW=true next build',
          );

          // Update the file content with proper formatting
          modifiedFiles[filePath] = JSON.stringify(packageContent, null, 2);
        }
      } catch (error) {
        console.warn(`Failed to parse package.json at ${filePath}:`, error);
      }
    }
  });

  // 2. Copy page route content to root page for preview
  if (pageRoute && pageRoute !== '/') {
    const sourcePagePath = `src/app${pageRoute}/page.tsx`;
    const targetPagePath = `src/app/page.tsx`;

    if (modifiedFiles[sourcePagePath]) {
      modifiedFiles[targetPagePath] = modifiedFiles[sourcePagePath];
    } else {
      console.warn(`Source page not found: ${sourcePagePath}`);
    }
  }

  // 3. Remove middleware.ts file for static build compatibility
  const middlewareFiles = [
    'middleware.ts',
    'src/middleware.ts',
    'app/middleware.ts',
    'src/app/middleware.ts',
  ];

  middlewareFiles.forEach((middlewarePath) => {
    if (modifiedFiles[middlewarePath]) {
      delete modifiedFiles[middlewarePath];
    }
  });

  // 4. Remove route.ts files (API routes) for static build compatibility
  // API routes can't be statically exported and will cause SSR-related crashes
  Object.keys(modifiedFiles).forEach((filePath) => {
    if (filePath.endsWith('route.ts')) {
      delete modifiedFiles[filePath];
    }
  });

  return modifiedFiles;
}

// ============================================================================
// NEW MIGRATION FUNCTIONS - Phase 1: Record<string, string> based utilities
// ============================================================================

/**
 * NEW: Converts AI message text directly to Record<string, string> format
 * Replaces: convertTextToWebContainerTree
 * @param message The AI message content to parse
 * @returns Record where keys are file paths and values are file contents
 */
export function convertTextToGitFiles(message: string): Record<string, string> {
  const gitFiles: Record<string, string> = {};

  // Match complete fullstackfoxAction blocks with type="file" and filePath and inner contents
  const completeActionRegex =
    /<fullstackfoxAction[^>]*type="file"[^>]*filePath="(.+?)"[^>]*>([\s\S]*?)<\/fullstackfoxAction>/g;

  // First process all complete matches
  const completeMatches = [...message.matchAll(completeActionRegex)];

  // Find all opening tags with type="file"
  const openingTagRegex =
    /<fullstackfoxAction[^>]*type="file"[^>]*filePath="(.+?)"[^>]*>/g;
  const allOpeningTags = [...message.matchAll(openingTagRegex)];

  // Find all closing tags
  const closingTagRegex = /<\/fullstackfoxAction>/g;
  const allClosingTags = [...message.matchAll(closingTagRegex)];

  // Combine complete matches with any incomplete matches
  const allMatches = [...completeMatches];

  // If we have more opening tags than closing tags, we have incomplete tags
  if (allOpeningTags.length > allClosingTags.length) {
    // For each opening tag, check if it has a corresponding closing tag
    for (let i = 0; i < allOpeningTags.length; i++) {
      const openingTag = allOpeningTags[i];
      const openingTagIndex = openingTag.index!;

      // Check if this opening tag is already part of a complete match
      const isPartOfCompleteMatch = completeMatches.some((match) => {
        // The match's index would be the same as the opening tag's index
        return match.index === openingTagIndex;
      });

      if (!isPartOfCompleteMatch) {
        // This is an incomplete tag, extract the file path
        const filePath = openingTag[1].trim();

        // Find the content between this opening tag and the end of the message or the next opening tag
        const tagContent = openingTag[0];
        const contentStartIndex = openingTagIndex + tagContent.length;

        // Find the next opening tag or the end of the message
        const nextOpeningTagIndex =
          i < allOpeningTags.length - 1
            ? allOpeningTags[i + 1].index
            : message.length;

        // Extract the content
        const fileContent = message
          .substring(contentStartIndex, nextOpeningTagIndex)
          .trim()
          // Remove any closing tags that might be in the content
          .replace(/<\/fullstackfoxAction>/g, '');

        // Create a synthetic match object
        const syntheticMatch = [
          '', // Full match (not needed)
          filePath,
          fileContent,
        ] as unknown as RegExpExecArray;
        syntheticMatch.index = openingTagIndex;

        allMatches.push(syntheticMatch);
      }
    }
  }

  // Process each match and add directly to gitFiles
  allMatches.forEach((match) => {
    const filePath = match[1].trim();
    const fileContent = match[2].trim();
    gitFiles[filePath] = fileContent;
  });

  return gitFiles;
}

/**
 * NEW: Merges two Record<string, string> objects
 * Replaces: mergeFileTrees
 * @param existing The existing git files
 * @param newFiles The new git files to merge in
 * @returns Merged git files with new files overwriting existing ones
 */
export function mergeGitFiles(
  existing: Record<string, string>,
  newFiles: Record<string, string>,
): Record<string, string> {
  return { ...existing, ...newFiles };
}

/**
 * NEW: Converts Record<string, string> to TreeDataItem[] for UI display
 * Replaces: convertFileTreeToTreeData
 * @param gitFiles Record of file paths to file contents
 * @param folderIcon Icon to use for folders
 * @param fileIcon Icon to use for files
 * @returns TreeDataItem structure for UI tree component
 */
export function convertGitFilesToTreeData(
  gitFiles: Record<string, string>,
  folderIcon?: LucideIcon,
  fileIcon?: LucideIcon,
): TreeDataItem[] | TreeDataItem {
  let idCounter = 1;

  const generateUniqueId = (): string => {
    return `node_${idCounter++}`;
  };

  // Build a tree structure from flat file paths
  const tree: { [key: string]: any } = {};

  Object.keys(gitFiles).forEach((filePath) => {
    const pathParts = filePath.split('/');
    let currentLevel = tree;

    pathParts.forEach((part, index) => {
      if (!currentLevel[part]) {
        currentLevel[part] = {
          isFile: index === pathParts.length - 1,
          children: {},
          path: pathParts.slice(0, index + 1).join('/'),
        };
      }
      if (index < pathParts.length - 1) {
        currentLevel = currentLevel[part].children;
      }
    });
  });

  // Convert tree structure to TreeDataItem[]
  const convertToTreeData = (
    node: any,
    name: string,
    path: string,
  ): TreeDataItem => {
    const children = Object.entries(node.children).map(([childName, child]) =>
      convertToTreeData(child, childName, (child as any).path),
    );

    return {
      id: generateUniqueId(),
      name,
      icon: node.isFile ? fileIcon : folderIcon,
      children: children.length > 0 ? children : undefined,
      path,
    };
  };

  const result = Object.entries(tree).map(([name, node]) =>
    convertToTreeData(node, name, (node as any).path),
  );

  // If there's only one root item, return it as a single TreeDataItem
  // Otherwise return the array of TreeDataItems
  return result.length === 1 ? result[0] : result;
}

/**
 * NEW: Gets file content by path from Record<string, string>
 * Replaces: getFileContentsByPath
 * @param gitFiles Record of file paths to file contents
 * @param path The file path to retrieve content for
 * @returns File content or null if not found
 */
export function getFileContentByPath(
  gitFiles: Record<string, string>,
  path: string,
): string | null {
  return gitFiles[path] || null;
}

/**
 * NEW: Filters out empty files from Record<string, string>
 * Replaces: filterEmptyFiles
 * @param gitFiles Record of file paths to file contents
 * @returns Filtered git files with empty files removed
 */
export function filterEmptyGitFiles(
  gitFiles: Record<string, string>,
): Record<string, string> {
  const filteredFiles: Record<string, string> = {};

  Object.entries(gitFiles).forEach(([filePath, content]) => {
    if (content.trim() !== '') {
      filteredFiles[filePath] = content;
    }
  });

  return filteredFiles;
}

/**
 * NEW: Removes files from Record<string, string> based on file paths
 * Replaces: removeFilesFromTree
 * @param gitFiles The existing git files
 * @param filesToDelete Array of file paths to delete
 * @returns Git files with specified files removed
 */
export function removeGitFiles(
  gitFiles: Record<string, string>,
  filesToDelete: string[],
): Record<string, string> {
  const updatedFiles = { ...gitFiles };

  filesToDelete.forEach((filePath) => {
    delete updatedFiles[filePath];
  });

  return updatedFiles;
}

/**
 * NEW: Filtered version of git files for ignored files
 * Replaces: convertFileSystemTreeToGitFilesFiltered
 * @param gitFiles Record of file paths to file contents
 * @returns Filtered git files with ignored files removed
 */
export function filterGitFilesForIgnored(
  gitFiles: Record<string, string>,
): Record<string, string> {
  return filterIgnoredFiles(gitFiles);
}
