import { TreeDataItem } from '@/components/ui/tree';
import { LucideIcon } from 'lucide-react';

// REMOVED: FileSystemTree - replaced with simple Record<string, string>

export type FileDeletions = {
  filesToDelete: string[];
};

export function extractFileDeletions(message: string): FileDeletions {
  // Match DELETE_FILE actions with filePath
  const deleteActionRegex =
    /<fullstackfoxAction[^>]*type="DELETE_FILE"[^>]*filePath="(.+?)"[^>]*(?:\/>|><\/fullstackfoxAction>)/g;

  const filesToDelete: string[] = [];
  let match;

  while ((match = deleteActionRegex.exec(message)) !== null) {
    const filePath = match[1].trim();
    if (filePath && !filesToDelete.includes(filePath)) {
      filesToDelete.push(filePath);
    }
  }

  return { filesToDelete };
}

/**
 * Extracts file paths from a message in the order they appear
 * @param message The message content to extract file paths from
 * @returns Array of file paths in the order they appear in the message
 */
export function extractFilePathsInOrder(message: string): string[] {
  // Match both complete and incomplete fullstackfoxAction blocks with type="file"
  const fileActionRegex =
    /<fullstackfoxAction[^>]*type="file"[^>]*filePath="(.+?)"[^>]*>/g;

  const filePaths: string[] = [];
  let match;

  while ((match = fileActionRegex.exec(message)) !== null) {
    const filePath = match[1].trim();
    if (filePath && !filePaths.includes(filePath)) {
      filePaths.push(filePath);
    }
  }

  return filePaths;
}

// REMOVED: convertTextToWebContainerTree - replaced by convertTextToFiles

// REMOVED: convertFileTreeToTreeData - replaced by convertGitFilesToTreeData

/**
 * Retrieves file contents from a FileSystemTree by path
 * @param fileTree The file system tree structure
 * @param path The path to the file (e.g. 'src/index.css')
 * @returns The file contents as a string, or null if the path doesn't exist or is a directory
 */
// REMOVED: mergeFileTrees - replaced by mergeFiles

// REMOVED: removeFilesFromTree - replaced by removeFiles

// REMOVED: getFileContentsByPath - replaced by getFileContentByPath
// REMOVED: filterEmptyFiles - replaced by filterEmptyFilesFromRecord

// REMOVED: convertGitFilesToFileSystemTree - no longer needed

// REMOVED: convertFileSystemTreeToGitFiles - no longer needed with new migration approach

/**
 * Checks if a file path should be ignored based on common ignore patterns
 * @param filePath The file path to check
 * @returns true if the file should be ignored, false otherwise
 */
export function shouldIgnoreFile(filePath: string): boolean {
  // Normalize path separators
  const normalizedPath = filePath.replace(/\\/g, '/');

  // Patterns to ignore
  const ignorePatterns = [
    // UI components directory
    /^src\/components\/ui\//,

    // Lock files
    /^pnpm-lock\.yaml$/,
    /^yarn\.lock$/,
    /^package-lock\.json$/,

    // Node modules
    /node_modules/,

    // Build outputs
    /\/dist\//,
    /\/build\//,
    /\/.next\//,
    /\/out\//,

    // Cache directories
    /\/.cache\//,
    /\.turbo/,

    // Environment files
    /\.env$/,
    /\.env\./,

    // IDE and editor files
    /\/.vscode\//,
    /\/.idea\//,
    /\.swp$/,
    /\.swo$/,
    /~$/,

    // OS generated files
    /\.DS_Store$/,
    /Thumbs\.db$/,
    /desktop\.ini$/,

    // Version control
    /\/.git\//,
    /\.gitignore$/,

    // Logs
    /\.log$/,
    /logs\//,
    /npm-debug\.log/,
    /yarn-debug\.log/,
    /yarn-error\.log/,
    /\.pnpm-debug\.log/,

    // Test coverage
    /\/coverage\//,
    /\.nyc_output\//,

    // Temporary files
    /\.tmp$/,
    /\.temp$/,
    /\/tmp\//,

    // Binary files and images (common ones)
    /\.(jpg|jpeg|png|gif|ico|svg|pdf|zip|tar|gz|exe|dmg)$/i,

    // TypeScript build info
    /\.tsbuildinfo$/,

    // Vercel
    /\/.vercel\//,
  ];

  return ignorePatterns.some((pattern) => pattern.test(normalizedPath));
}

/**
 * Filters out ignored files from gitFiles object
 * @param gitFiles Record of file paths to file contents
 * @returns Filtered gitFiles object with ignored files removed
 */
export function filterIgnoredFiles(
  gitFiles: Record<string, string>,
): Record<string, string> {
  const filteredFiles: Record<string, string> = {};

  for (const [filePath, content] of Object.entries(gitFiles)) {
    // Always include package.json files for dependency information
    const isPackageJson = filePath.endsWith('package.json');

    if (isPackageJson || !shouldIgnoreFile(filePath)) {
      filteredFiles[filePath] = content;
    }
  }

  return filteredFiles;
}

// REMOVED: convertGitFilesToFileSystemTreeFiltered - no longer needed

// REMOVED: convertFileSystemTreeToGitFilesFiltered - replaced by filterGitFilesForIgnored

/**
 * Modifies package.json files in gitFiles to add STATIC_PREVIEW=true to build scripts
 * @param gitFiles Record of file paths to file contents
 * @returns Modified gitFiles with updated package.json build scripts
 */
export function modifyPackageJsonForPreview(
  gitFiles: Record<string, string>,
): Record<string, string> {
  const modifiedFiles = { ...gitFiles };

  // Find all package.json files
  Object.keys(modifiedFiles).forEach((filePath) => {
    if (filePath.endsWith('package.json')) {
      try {
        const packageContent = JSON.parse(modifiedFiles[filePath]);

        // Check if scripts.build exists and contains "next build"
        if (packageContent.scripts?.build?.includes('next build')) {
          packageContent.scripts.build = packageContent.scripts.build.replace(
            'next build',
            'STATIC_PREVIEW=true next build',
          );

          // Update the file content with proper formatting
          modifiedFiles[filePath] = JSON.stringify(packageContent, null, 2);
        }
      } catch (error) {
        console.warn(`Failed to parse package.json at ${filePath}:`, error);
      }
    }
  });

  return modifiedFiles;
}

/**
 * Modifies files for preview mode: updates package.json build scripts, copies page route to root, and removes middleware.ts
 * @param gitFiles Record of file paths to file contents
 * @param pageRoute Optional page route (e.g., "/auth/login") to copy to root page
 * @returns Modified gitFiles with updated package.json, copied page content, and removed middleware files
 */
export function modifyFilesForPreview(
  gitFiles: Record<string, string>,
  pageRoute?: string,
): Record<string, string> {
  const modifiedFiles = { ...gitFiles };

  // 1. Modify package.json files for preview mode
  Object.keys(modifiedFiles).forEach((filePath) => {
    if (filePath.endsWith('package.json')) {
      try {
        const packageContent = JSON.parse(modifiedFiles[filePath]);

        // Check if scripts.build exists and contains "next build"
        if (packageContent.scripts?.build?.includes('next build')) {
          packageContent.scripts.build = packageContent.scripts.build.replace(
            'next build',
            'STATIC_PREVIEW=true next build',
          );

          // Update the file content with proper formatting
          modifiedFiles[filePath] = JSON.stringify(packageContent, null, 2);
        }
      } catch (error) {
        console.warn(`Failed to parse package.json at ${filePath}:`, error);
      }
    }
  });

  // 2. Copy page route content to root page for preview
  if (pageRoute && pageRoute !== '/') {
    const sourcePagePath = `src/app${pageRoute}/page.tsx`;
    const targetPagePath = `src/app/page.tsx`;

    if (modifiedFiles[sourcePagePath]) {
      modifiedFiles[targetPagePath] = modifiedFiles[sourcePagePath];
    } else {
      console.warn(`Source page not found: ${sourcePagePath}`);
    }
  }

  // 3. Remove middleware.ts file for static build compatibility
  const middlewareFiles = [
    'middleware.ts',
    'src/middleware.ts',
    'app/middleware.ts',
    'src/app/middleware.ts',
  ];

  middlewareFiles.forEach((middlewarePath) => {
    if (modifiedFiles[middlewarePath]) {
      delete modifiedFiles[middlewarePath];
    }
  });

  // 4. Remove route.ts files (API routes) for static build compatibility
  // API routes can't be statically exported and will cause SSR-related crashes
  Object.keys(modifiedFiles).forEach((filePath) => {
    if (filePath.endsWith('route.ts')) {
      delete modifiedFiles[filePath];
    }
  });

  return modifiedFiles;
}

// ============================================================================
// NEW MIGRATION FUNCTIONS - Phase 1: Record<string, string> based utilities
// ============================================================================

/**
 * NEW: Converts AI message text directly to Record<string, string> format
 * Replaces: convertTextToWebContainerTree
 * @param message The AI message content to parse
 * @returns Record where keys are file paths and values are file contents
 */
export function convertTextToFiles(message: string): Record<string, string> {
  const files: Record<string, string> = {};

  // Match complete fullstackfoxAction blocks with type="file" and filePath and inner contents
  const completeActionRegex =
    /<fullstackfoxAction[^>]*type="file"[^>]*filePath="(.+?)"[^>]*>([\s\S]*?)<\/fullstackfoxAction>/g;

  // First process all complete matches
  const completeMatches = [...message.matchAll(completeActionRegex)];

  // Find all opening tags with type="file"
  const openingTagRegex =
    /<fullstackfoxAction[^>]*type="file"[^>]*filePath="(.+?)"[^>]*>/g;
  const allOpeningTags = [...message.matchAll(openingTagRegex)];

  // Find all closing tags
  const closingTagRegex = /<\/fullstackfoxAction>/g;
  const allClosingTags = [...message.matchAll(closingTagRegex)];

  // Combine complete matches with any incomplete matches
  const allMatches = [...completeMatches];

  // If we have more opening tags than closing tags, we have incomplete tags
  if (allOpeningTags.length > allClosingTags.length) {
    // For each opening tag, check if it has a corresponding closing tag
    for (let i = 0; i < allOpeningTags.length; i++) {
      const openingTag = allOpeningTags[i];
      const openingTagIndex = openingTag.index!;

      // Check if this opening tag is already part of a complete match
      const isPartOfCompleteMatch = completeMatches.some((match) => {
        // The match's index would be the same as the opening tag's index
        return match.index === openingTagIndex;
      });

      if (!isPartOfCompleteMatch) {
        // This is an incomplete tag, extract the file path
        const filePath = openingTag[1].trim();

        // Find the content between this opening tag and the end of the message or the next opening tag
        const tagContent = openingTag[0];
        const contentStartIndex = openingTagIndex + tagContent.length;

        // Find the next opening tag or the end of the message
        const nextOpeningTagIndex =
          i < allOpeningTags.length - 1
            ? allOpeningTags[i + 1].index
            : message.length;

        // Extract the content
        const fileContent = message
          .substring(contentStartIndex, nextOpeningTagIndex)
          .trim()
          // Remove any closing tags that might be in the content
          .replace(/<\/fullstackfoxAction>/g, '');

        // Create a synthetic match object
        const syntheticMatch = [
          '', // Full match (not needed)
          filePath,
          fileContent,
        ] as unknown as RegExpExecArray;
        syntheticMatch.index = openingTagIndex;

        allMatches.push(syntheticMatch);
      }
    }
  }

  // Process each match and add directly to files
  allMatches.forEach((match) => {
    const filePath = match[1].trim();
    const fileContent = match[2].trim();
    files[filePath] = fileContent;
  });

  return files;
}

/**
 * NEW: Merges two Record<string, string> objects
 * Replaces: mergeFileTrees
 * @param existing The existing files
 * @param newFiles The new files to merge in
 * @returns Merged files with new files overwriting existing ones
 */
export function mergeFiles(
  existing: Record<string, string>,
  newFiles: Record<string, string>,
): Record<string, string> {
  return { ...existing, ...newFiles };
}

/**
 * NEW: Converts Record<string, string> to TreeDataItem[] for UI display
 * Replaces: convertFileTreeToTreeData
 * @param files Record of file paths to file contents
 * @param folderIcon Icon to use for folders
 * @param fileIcon Icon to use for files
 * @returns TreeDataItem structure for UI tree component
 */
export function convertFilesToTreeData(
  files: Record<string, string>,
  folderIcon?: LucideIcon,
  fileIcon?: LucideIcon,
): TreeDataItem[] | TreeDataItem {
  let idCounter = 1;

  const generateUniqueId = (): string => {
    return `node_${idCounter++}`;
  };

  // Build a tree structure from flat file paths
  const tree: { [key: string]: any } = {};

  Object.keys(files).forEach((filePath) => {
    const pathParts = filePath.split('/');
    let currentLevel = tree;

    pathParts.forEach((part, index) => {
      if (!currentLevel[part]) {
        currentLevel[part] = {
          isFile: index === pathParts.length - 1,
          children: {},
          path: pathParts.slice(0, index + 1).join('/'),
        };
      }
      if (index < pathParts.length - 1) {
        currentLevel = currentLevel[part].children;
      }
    });
  });

  // Convert tree structure to TreeDataItem[]
  const convertToTreeData = (
    node: any,
    name: string,
    path: string,
  ): TreeDataItem => {
    const children = Object.entries(node.children).map(([childName, child]) =>
      convertToTreeData(child, childName, (child as any).path),
    );

    return {
      id: generateUniqueId(),
      name,
      icon: node.isFile ? fileIcon : folderIcon,
      children: children.length > 0 ? children : undefined,
      path,
    };
  };

  const result = Object.entries(tree).map(([name, node]) =>
    convertToTreeData(node, name, (node as any).path),
  );

  // If there's only one root item, return it as a single TreeDataItem
  // Otherwise return the array of TreeDataItems
  return result.length === 1 ? result[0] : result;
}

/**
 * NEW: Gets file content by path from Record<string, string>
 * Replaces: getFileContentsByPath
 * @param files Record of file paths to file contents
 * @param path The file path to retrieve content for
 * @returns File content or null if not found
 */
export function getFileContentByPath(
  files: Record<string, string>,
  path: string,
): string | null {
  return files[path] || null;
}

/**
 * NEW: Filters out empty files from Record<string, string>
 * Replaces: filterEmptyFiles (for FileSystemTree)
 * @param files Record of file paths to file contents
 * @returns Filtered files with empty files removed
 */
export function filterEmptyFilesFromRecord(
  files: Record<string, string>,
): Record<string, string> {
  const filteredFiles: Record<string, string> = {};

  Object.entries(files).forEach(([filePath, content]) => {
    if (content.trim() !== '') {
      filteredFiles[filePath] = content;
    }
  });

  return filteredFiles;
}

/**
 * NEW: Removes files from Record<string, string> based on file paths
 * Replaces: removeFilesFromTree
 * @param files The existing files
 * @param filesToDelete Array of file paths to delete
 * @returns Files with specified files removed
 */
export function removeFiles(
  files: Record<string, string>,
  filesToDelete: string[],
): Record<string, string> {
  const updatedFiles = { ...files };

  filesToDelete.forEach((filePath) => {
    delete updatedFiles[filePath];
  });

  return updatedFiles;
}

/**
 * NEW: Filtered version of files for ignored files
 * Replaces: convertFileSystemTreeToGitFilesFiltered
 * @param files Record of file paths to file contents
 * @returns Filtered files with ignored files removed
 */
export function filterFilesForIgnored(
  files: Record<string, string>,
): Record<string, string> {
  return filterIgnoredFiles(files);
}
