import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Logger,
  Post,
} from '@nestjs/common';
import { PreviewService } from './preview.service';

@Controller('preview')
export class PreviewController {
  private readonly logger = new Logger(PreviewController.name);

  constructor(private readonly previewService: PreviewService) {}

  @Post('upload')
  async createPreview(@Body() files: Record<string, string>) {
    this.logger.log('Received preview creation request');

    try {
      if (!files || typeof files !== 'object') {
        throw new HttpException(
          'Invalid files provided',
          HttpStatus.BAD_REQUEST,
        );
      }

      const result = await this.previewService.createPreview(files);

      this.logger.log(`Preview created successfully: ${result.previewUrl}`);
      return result;
    } catch (error) {
      this.logger.error('Preview creation failed:', error);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        `Preview creation failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('health')
  async healthCheck() {
    return this.previewService.healthCheck();
  }
}
